spring:
  thymeleaf:
    cache: false
    check-template-location: true
    prefix: classpath:/templates/
    suffix: .html
  devtools:
    livereload:
      enabled: false
  freemarker:
    cache: false
  web:
    resources:
      add-mappings: false
  datasource:
    # 테스트용 H2 인메모리 데이터베이스 설정
    url: jdbc:h2:mem:testdb;MODE=MySQL;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  h2:
    console:
      enabled: true
  servlet:
    multipart:
      max-file-size: 30MB
      max-request-size: 30MB

server:
  port: 0  # 랜덤 포트 사용
  error:
    whitelabel:
      enabled: false
    path: /error
    include-stacktrace: never

mybatis:
  type-aliases-package: kr.wayplus.qr_hallimpark.model
  configuration:
    default-statement-timeout: 30
    auto-mapping-unknown-column-behavior: warning
    map-underscore-to-camel-case: true
  mapper-locations: classpath:/sqlmapper/

# 테스트용 설정값들
cookie-set:
  domain: "localhost"
  prefix: "test"
  tracking: false
  tracking-day: 1

api-keys:
  kakao:
    rest: "test-key"
    javascript: "test-key"
  publicdata:
    devel: "test-key"
    service: "test-key"

upload:
  path:
    file: "/tmp/test/file/"
    image: "/tmp/test/image/"
  image:
    max-size: 10485760
  file:
    max-size: 104857600

using:
  spring:
    schedulerFactory: false

trace: false
debug: false

# 로깅 설정
logging:
  level:
    kr.wayplus.qr_hallimpark: DEBUG
    org.springframework.web: DEBUG
    org.mybatis: DEBUG
