package kr.wayplus.qr_hallimpark.controller.api;

import kr.wayplus.qr_hallimpark.model.external.QrQuizMapping;
import kr.wayplus.qr_hallimpark.service.QrQuizMappingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;

/**
 * qr-quiz API 컨트롤러
 * - API 서버에서 호출하는 QR-문제 연결 관련 API
 * - 서버 간 통신: API 키 인증 필요 (WayQRConnect)
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class QrQuizMappingController {

    private final QrQuizMappingService qrQuizMappingService;

    /**
     * QR-문제 연결 생성 (API 서버용)
     * @param request 연결 생성 요청
     * @return 연결 생성 결과
     */
    @PostMapping("/qr-quiz/mapping")
    public HashMap<String, Object> mappingQrQuiz(@RequestBody QrQuizMapping mapping) {
        log.debug("QR-Quiz mapping: {}", mapping);

        HashMap<String, Object> response = new HashMap<>();

        try {
            // 요청 데이터 유효성 검증
            validateCreateRequest(mapping);

            // 연결 생성
            QrQuizMapping createdMapping = qrQuizMappingService.createMapping(mapping);

            // 생성된 매핑 정보 조회 (조인 정보 포함)
            QrQuizMapping fullMapping = qrQuizMappingService.findMappingById(createdMapping.getMappingId());

            // 응답 데이터 생성
            QrQuizMapping.QrQuizMappingResponse mappingResponse = QrQuizMapping.QrQuizMappingResponse.fromQrQuizMapping(fullMapping);

            response.put("success", true);
            response.put("message", "연결이 성공적으로 생성되었습니다.");
            response.put("data", mappingResponse);

            log.info("QR-Quiz mapping created successfully. MappingId: {}, qrCodeId: {}, QuizId: {}", 
                    createdMapping.getMappingId(), mapping.getQrCodeId(), mapping.getQuizId());

        } catch (IllegalArgumentException e) {
            log.warn("Invalid qr-quiz mapping request: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_REQUEST");

        } catch (Exception e) {
            log.error("Error QR-Quiz mapping: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "연결 생성 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * QR-문제 연결 해제 (API 서버용)
     * - QR ID와 문제 ID로 특정 연결 해제
     * - 매핑 ID로 직접 해제도 지원
     * @param request 연결 해제 요청
     * @return 연결 해제 결과
     */
    @PostMapping("/qr-quiz/un-mapping")
    public HashMap<String, Object> unMappingQrQuiz(@RequestBody QrQuizMapping.QrQuizUnMappingRequest request) {
        log.debug("UnMapping QR-Quiz : {}", request);

        HashMap<String, Object> response = new HashMap<>();

        try {
            // 요청 데이터 유효성 검증
            validateDeleteRequest(request);

            QrQuizMapping deletedMapping;
            QrQuizMapping.QrQuizUnMappingResponse deleteResponse;

            // QR ID와 문제 ID로 삭제
            deletedMapping = qrQuizMappingService.deleteMappingByQrCodeIdAndQuizId(
                    request.getQrCodeId(), request.getQuizId(), request.getCreateId());

            deleteResponse = QrQuizMapping.QrQuizUnMappingResponse.fromQrQuizMapping(deletedMapping, request.getDeleteReason());

            log.info("QR-Quiz unMapped by qrCodeId and QuizId. qrCodeId: {}, QuizId: {}, MappingId: {}",
                    request.getQrCodeId(), request.getQuizId(), deletedMapping.getMappingId());
            

            response.put("success", true);
            response.put("message", "연결이 성공적으로 해제되었습니다.");
            response.put("data", deleteResponse);

        } catch (IllegalArgumentException e) {
            log.warn("Invalid unMapping request: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_REQUEST");

        } catch (Exception e) {
            log.error("Error unMapping QR-Quiz: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "연결 해제 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * 연결 생성 요청 유효성 검증
     * @param mapping 요청 데이터
     */
    private void validateCreateRequest(QrQuizMapping mapping) {
        if (mapping == null) {
            throw new IllegalArgumentException("요청 데이터는 필수입니다.");
        }

        if (mapping.getQrCodeId() == null || mapping.getQrCodeId() <= 0) {
            throw new IllegalArgumentException("QR ID는 필수입니다.");
        }

        if (mapping.getQuizId() == null) {
            throw new IllegalArgumentException("문제 ID는 필수입니다.");
        }
    }



    /**
     * 연결 해제 요청 유효성 검증
     * @param request 요청 데이터
     */
    private void validateDeleteRequest(QrQuizMapping.QrQuizUnMappingRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("요청 데이터는 필수입니다.");
        }

        if (!request.isValid()) {
            throw new IllegalArgumentException("매핑 ID 또는 QR ID와 문제 ID가 필요합니다.");
        }

        if (request.getQuizId() <= 0) {
            throw new IllegalArgumentException("유효하지 않은 문제 ID입니다.");
        }
    }
}
