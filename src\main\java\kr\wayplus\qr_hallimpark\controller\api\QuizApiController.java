package kr.wayplus.qr_hallimpark.controller.api;

import kr.wayplus.qr_hallimpark.model.AdminListResponse;
import kr.wayplus.qr_hallimpark.model.AdminListSearch;
import kr.wayplus.qr_hallimpark.model.Quiz;
import kr.wayplus.qr_hallimpark.model.external.QuizApiResponse;
import kr.wayplus.qr_hallimpark.service.QuizService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Quiz API 컨트롤러
 * - 문제 관련 API
 * - CORS 설정으로 외부 도메인 접근 허용
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
@CrossOrigin(origins = "http://192.168.0.227:9998", allowCredentials = "true")
public class QuizApiController {

    private final QuizService quizService;

    /**
     * 문제 목록 조회 
     * @param page 페이지 번호 (기본값: 1)
     * @param size 페이지 크기 (기본값: 20)
     * @param categoryId 카테고리 ID (선택사항)
     * @return 문제 목록 응답
     */
    @GetMapping("/quizzes")
    public HashMap<String, Object> getQuizzes(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "size", defaultValue = "20") Integer size,
            @RequestParam(value = "categoryId", required = false) Long categoryId) {
        
        log.debug("page: {}, size: {}, categoryId: {}", page, size, categoryId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            // 검색 조건 생성
            AdminListSearch searchCondition = AdminListSearch.builder()
                    .page(page)
                    .size(Math.min(size, 100)) // 최대 100개로 제한
                    .sortField("quiz_id")
                    .sortDirection("DESC") // 최신 순
                    .baseCondition("qm.delete_yn = 'N' AND cat.delete_yn = 'N' AND qm.status = 'ACTIVE'") // 활성화된 문제만
                    .tableName("quiz_master")
                    .build();

            // 카테고리 필터 추가
            if (categoryId != null) {
                String categoryFilter = "qm.category_id = " + categoryId;
                String existingCondition = searchCondition.getBaseCondition();
                searchCondition.setBaseCondition(existingCondition + " AND " + categoryFilter);
            }

            // 문제 목록 조회
            AdminListResponse<Quiz> quizListResponse = quizService.findListWithConditions(searchCondition);

            // 응답 형식으로 변환
            List<QuizApiResponse> apiQuizzes = quizListResponse.getItems().stream()
                    .map(QuizApiResponse::fromQuiz)
                    .collect(Collectors.toList());

            // 응답 데이터 구성
            HashMap<String, Object> data = new HashMap<>();
            data.put("items", apiQuizzes);
            data.put("currentPage", quizListResponse.getCurrentPage());
            data.put("totalPages", quizListResponse.getTotalPages());
            data.put("totalCount", quizListResponse.getTotalCount());
            data.put("hasNext", quizListResponse.getHasNext());
            data.put("hasPrevious", quizListResponse.getHasPrevious());
            data.put("pageSize", quizListResponse.getPageSize());

            response.put("success", true);
            response.put("data", data);

            log.debug("Successfully retrieved {} quizzes", apiQuizzes.size());

        } catch (IllegalArgumentException e) {
            log.warn("Invalid request parameters: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_PARAMETERS");

        } catch (Exception e) {
            log.error("Error retrieving quizzes : {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "문제 목록 조회 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * 단일 문제 조회 
     * @param quizId 문제 ID
     * @return 문제 정보
     */
    @GetMapping("/quizzes/{quizId}")
    public HashMap<String, Object> getQuiz(@PathVariable("quizId") Long quizId) {
        log.debug("Getting quiz by ID: {}", quizId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            Quiz quiz = quizService.findQuizById(quizId);
            
            // 활성화된 문제만 반환
            if (!"ACTIVE".equals(quiz.getStatus())) {
                response.put("success", false);
                response.put("message", "비활성화된 문제입니다.");
                response.put("errorCode", "QUIZ_INACTIVE");
                return response;
            }

            QuizApiResponse apiQuiz = QuizApiResponse.fromQuiz(quiz);

            response.put("success", true);
            response.put("data", apiQuiz);

            log.debug("Successfully retrieved quiz: {}", quizId);

        } catch (IllegalArgumentException e) {
            log.warn("Quiz not found: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "QUIZ_NOT_FOUND");

        } catch (Exception e) {
            log.error("Error retrieving quiz: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "문제 조회 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }
}
