package kr.wayplus.qr_hallimpark.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * CORS 필터
 * - 외부 프론트엔드 요청에 대한 CORS 헤더 설정
 * - 높은 우선순위로 실행되어 모든 요청에 적용
 */
@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class CorsFilter implements Filter {

    private static final String ALLOWED_ORIGIN = "http://192.168.0.227:9998";

    @Override
    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain)
            throws IOException, ServletException {

        HttpServletRequest request = (HttpServletRequest) req;
        HttpServletResponse response = (HttpServletResponse) res;

        String origin = request.getHeader("Origin");
        String requestURI = request.getRequestURI();
        String method = request.getMethod();

        log.info("CORS Filter - URI: {}, Method: {}, Origin: {}", requestURI, method, origin);

        // 공개 API 경로에 대해서만 CORS 헤더 설정
        if (requestURI.startsWith("/api/public/") || requestURI.startsWith("/api/quizzes/")) {

            log.info("Processing CORS for public API: {}", requestURI);

            // 허용된 Origin인지 확인
            if (ALLOWED_ORIGIN.equals(origin)) {
                log.info("Setting CORS headers for request: {} from origin: {}", requestURI, origin);

                // CORS 헤더 설정
                response.setHeader("Access-Control-Allow-Origin", origin);
                response.setHeader("Access-Control-Allow-Credentials", "true");
                response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
                response.setHeader("Access-Control-Allow-Headers",
                    "Origin, X-Requested-With, Content-Type, Accept, Authorization, X-API-Key");
                response.setHeader("Access-Control-Max-Age", "3600");

                // Preflight 요청 처리
                if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
                    log.info("Handling OPTIONS preflight request for: {}", requestURI);
                    response.setStatus(HttpServletResponse.SC_OK);
                    return;
                }
            } else {
                log.warn("CORS request from unauthorized origin: {} for URI: {}", origin, requestURI);
            }
        } else {
            log.info("URI {} does not match public API pattern, skipping CORS", requestURI);
        }

        chain.doFilter(request, response);
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("CORS Filter initialized");
    }

    @Override
    public void destroy() {
        log.info("CORS Filter destroyed");
    }
}
