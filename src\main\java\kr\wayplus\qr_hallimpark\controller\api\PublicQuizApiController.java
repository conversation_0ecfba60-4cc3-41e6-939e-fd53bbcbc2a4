package kr.wayplus.qr_hallimpark.controller.api;

import kr.wayplus.qr_hallimpark.model.external.QrQuizMapping;
import kr.wayplus.qr_hallimpark.service.QrQuizMappingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Public Quiz API 컨트롤러
 * - 외부 프론트엔드에서 호출하는 공개 API (API 키 인증 불필요)
 * - 읽기 전용 작업만 허용 (보안 강화)
 * - CORS 설정으로 특정 도메인만 허용
 */
@Slf4j
@RestController
@RequestMapping("/api/public")
@RequiredArgsConstructor
@CrossOrigin(
    origins = {"http://192.168.0.227:9998"},
    allowedHeaders = {"*"},
    methods = {RequestMethod.GET, RequestMethod.OPTIONS},
    allowCredentials = "true",
    maxAge = 3600
)
public class PublicQuizApiController {

    private final QrQuizMappingService qrQuizMappingService;

    /**
     * 여러 QR 코드의 연결된 문제 목록 조회 (공개 API)
     * - 활성화된 문제만 조회
     * @param qrCodeIds QR 코드 ID 목록 (배열 또는 쉼표 구분 문자열)
     * @param sortBy 정렬 옵션
     * @param activeOnly 활성화된 문제만 조회할지 여부
     * @param simple 단순 응답 형식 사용 여부 (true: quizzes 배열만 반환, false: 전체 구조 반환)
     * @return QR 코드별 연결된 문제 목록
     */
    @GetMapping("/qr-quiz-mapping-list")
    public HashMap<String, Object> getQrQuizMappingList(
            @RequestParam(value = "qrCodeIds", required = true) String qrCodeIds,
            @RequestParam(value = "sortBy", defaultValue = "displayOrder") String sortBy,
            @RequestParam(value = "activeOnly", defaultValue = "true") Boolean activeOnly,
            @RequestParam(value = "simple", defaultValue = "false") Boolean simple) {
        // qrCodeIds 문자열을 List로 변환 (쉼표로 구분된 값 처리)
        List<String> finalQrCodeIds = Arrays.stream(qrCodeIds.split(","))
                .map(String::trim)
                .filter(id -> !id.isEmpty())
                .collect(Collectors.toList());

        // QrQuizMapping.QrQuizInfoRequest 객체 생성
        QrQuizMapping.QrQuizInfoRequest request = QrQuizMapping.QrQuizInfoRequest.builder()
                .qrCodeIds(finalQrCodeIds)
                .sortBy(sortBy)
                .activeOnly(activeOnly)
                .build();

        log.debug("Getting public qr-quiz batch: qrCodeIds={}, sortBy={}, activeOnly={}",
                finalQrCodeIds, sortBy, activeOnly);

        HashMap<String, Object> response = new HashMap<>();

        try {
            // 요청 데이터 유효성 검증
            validatePublicQrQuizInfoRequest(request);

            // 보안을 위해 활성화된 문제만 강제 조회
            List<QrQuizMapping> allMappings = qrQuizMappingService.findMappingsByQrCodeIds(
                    request.getQrCodeIds(),
                    true // 항상 활성화된 문제만 조회
            );

            // QR ID별로 그룹화
            Map<Long, List<QrQuizMapping>> groupedMappings = allMappings.stream()
                    .collect(Collectors.groupingBy(QrQuizMapping::getQrCodeId));

            // simple 모드인 경우 단순한 응답 구조 사용
            if (simple && request.getQrCodeIds().size() == 1) {
                // 단일 QR 코드 조회 시 quizzes 배열만 반환
                Long qrCodeId = Long.parseLong(request.getQrCodeIds().get(0));
                List<QrQuizMapping> mappings = groupedMappings.getOrDefault(qrCodeId, List.of());
                List<QrQuizMapping.QuizInfo> quizInfos = mappings.stream()
                        .map(this::createSafeQuizInfo)
                        .collect(Collectors.toList());

                response.put("success", true);
                response.put("data", quizInfos); // 직접 quizzes 배열 반환
                response.put("quizCount", quizInfos.size());
            } else {
                // 기존 응답 구조 유지 (여러 QR 코드 또는 simple=false인 경우)
                List<QrQuizMapping.QrQuizInfoResponse> batchResponses = request.getQrCodeIds().stream()
                        .map(qrCodeIdStr -> {
                            Long qrCodeId = Long.parseLong(qrCodeIdStr);
                            List<QrQuizMapping> mappings = groupedMappings.getOrDefault(qrCodeId, List.of());
                            List<QrQuizMapping.QuizInfo> quizInfos = mappings.stream()
                                    .map(this::createSafeQuizInfo) // 민감한 정보 제거
                                    .collect(Collectors.toList());

                            return QrQuizMapping.QrQuizInfoResponse.builder()
                                    .qrCodeId(qrCodeId)
                                    .quizzes(quizInfos)
                                    .quizCount(quizInfos.size())
                                    .build();
                        })
                        .collect(Collectors.toList());

                response.put("success", true);
                response.put("data", batchResponses);
                response.put("totalQrCount", request.getQrCodeIds().size());
                response.put("totalQuizCount", allMappings.size());
            }

            log.debug("Successfully retrieved public batch qr-quiz for {} QR codes, {} total quizzes",
                    request.getQrCodeIds().size(), allMappings.size());

        } catch (IllegalArgumentException e) {
            log.warn("Invalid public batch request: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_REQUEST");

        } catch (Exception e) {
            log.error("Error retrieving public batch qr-quiz: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "조회 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * 단일 QR 코드의 연결된 문제 목록 조회 (간단한 응답)
     * - 활성화된 문제만 조회
     * - response.data로 바로 접근 가능
     * @param qrCodeId QR 코드 ID
     * @param sortBy 정렬 옵션
     * @return 문제 목록 (배열 형태)
     */
    @GetMapping("/qr-quiz/{qrCodeId}")
    public HashMap<String, Object> getQrQuizSimple(
            @PathVariable("qrCodeId") String qrCodeId,
            @RequestParam(value = "sortBy", defaultValue = "displayOrder") String sortBy) {

        log.debug("Getting simple qr-quiz: qrCodeId={}, sortBy={}", qrCodeId, sortBy);

        HashMap<String, Object> response = new HashMap<>();

        try {
            // 단일 QR 코드 조회
            List<QrQuizMapping> mappings = qrQuizMappingService.findMappingsByQrCodeIds(
                    List.of(qrCodeId),
                    true // 활성화된 문제만 조회
            );

            // 안전한 문제 정보 생성
            List<QrQuizMapping.QuizInfo> quizInfos = mappings.stream()
                    .map(this::createSafeQuizInfo)
                    .collect(Collectors.toList());

            response.put("success", true);
            response.put("data", quizInfos); // 직접 배열 반환
            response.put("quizCount", quizInfos.size());

            log.debug("Successfully retrieved simple qr-quiz for QR {}, {} quizzes found",
                    qrCodeId, quizInfos.size());

        } catch (Exception e) {
            log.error("Error retrieving simple qr-quiz for QR {}: {}", qrCodeId, e.getMessage(), e);
            response.put("success", false);
            response.put("message", "조회 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * CORS Preflight 요청 처리 (OPTIONS 메서드)
     * @return 성공 응답
     */
    @RequestMapping(value = "/**", method = RequestMethod.OPTIONS)
    public HashMap<String, Object> handleOptions() {
        HashMap<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "CORS preflight request handled successfully");
        return response;
    }

    /**
     * 공개 API용 안전한 문제 정보 생성 (민감한 정보 제외)
     * @param mapping QrQuizMapping 객체
     * @return 안전한 QuizInfo 객체
     */
    private QrQuizMapping.QuizInfo createSafeQuizInfo(QrQuizMapping mapping) {
        return QrQuizMapping.QuizInfo.builder()
                .mappingId(mapping.getMappingId())
                .quizId(mapping.getQuizId())
                .qrCodeId(mapping.getQrCodeId())
                .quizTitle(mapping.getQuizTitle())
                .question(mapping.getQuestion())
                .quizType(mapping.getQuizType())
                .quizStatus(mapping.getQuizStatus())
                .categoryName(mapping.getCategoryName())
                .difficultyLevel(mapping.getDifficultyLevel())
                .displayOrder(mapping.getDisplayOrder())
                .correctAnswer(null) // 정답은 공개하지 않음
                .imageUrl(mapping.getImageUrl())
                .hint(mapping.getHint())
                .build();
    }

    /**
     * 공개 API 배치 요청 유효성 검증
     * @param request 요청 데이터
     */
    private void validatePublicQrQuizInfoRequest(QrQuizMapping.QrQuizInfoRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("요청 데이터는 필수입니다.");
        }

        if (request.getQrCodeIds() == null || request.getQrCodeIds().isEmpty()) {
            throw new IllegalArgumentException("QR ID 목록은 필수입니다.");
        }

        if (request.getQrCodeIds().size() > 50) { // 공개 API는 더 제한적
            throw new IllegalArgumentException("한 번에 조회할 수 있는 QR 코드는 최대 50개입니다.");
        }

        // 빈 문자열이나 null 값 체크
        long validQrCodeIdCount = request.getQrCodeIds().stream()
                .filter(qrCodeId -> qrCodeId != null && !qrCodeId.trim().isEmpty())
                .count();

        if (validQrCodeIdCount == 0) {
            throw new IllegalArgumentException("유효한 QR ID가 없습니다.");
        }
    }

}
