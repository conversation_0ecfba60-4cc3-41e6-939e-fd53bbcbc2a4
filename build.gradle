plugins {
	id 'java'
	id 'org.springframework.boot' version '3.4.6'
	id 'io.spring.dependency-management' version '1.1.7'
}

group = 'kr.wayplus'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(17)
	}
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	mavenCentral()
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:3.0.4'
	implementation 'org.thymeleaf.extras:thymeleaf-extras-springsecurity6'
	implementation 'nz.net.ultraq.thymeleaf:thymeleaf-layout-dialect'
	compileOnly 'org.projectlombok:lombok'
	developmentOnly 'org.springframework.boot:spring-boot-devtools'
	runtimeOnly 'com.mysql:mysql-connector-j'
	runtimeOnly 'org.mariadb.jdbc:mariadb-java-client'
	annotationProcessor 'org.projectlombok:lombok'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter-test:3.0.4'
	testImplementation 'org.springframework.security:spring-security-test'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
	testRuntimeOnly 'com.h2database:h2'
}

tasks.named('test') {
	useJUnitPlatform()
}
task runDev(type: JavaExec) {
	group = 'application'
	main = 'kr.wayplus.qr_hallimpark.QrHallimparkApplication'
	classpath = sourceSets.main.runtimeClasspath
	args = ['--spring.profiles.active=dev']
}