package kr.wayplus.qr_hallimpark.service;

import kr.wayplus.qr_hallimpark.mapper.QrQuizMappingMapper;
import kr.wayplus.qr_hallimpark.model.external.QrQuizMapping;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * QR-문제 매핑 서비스
 * - QR 코드와 문제 간의 연결 정보 CRUD 비즈니스 로직 처리
 * - 유효성 검증 및 중복 체크
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class QrQuizMappingService {
    
    private final QrQuizMappingMapper qrQuizMappingMapper;
    private final QuizService quizService;

    /**
     * QR ID로 매핑 목록 조회
     * @param qrCodeId QR 코드 ID
     * @return 매핑 목록
     */
    public List<QrQuizMapping> findMappingsByQrCodeId(Long qrCodeId) {
        log.debug("Finding mappings by QR ID: {}", qrCodeId);
        
        if (qrCodeId == null || qrCodeId <= 0) {
            throw new IllegalArgumentException("QR ID는 필수입니다.");
        }
        
        return qrQuizMappingMapper.selectMappingsByQrCodeId(qrCodeId);
    }

    /**
     * 여러 QR ID로 매핑 목록 조회
     * @param qrCodeIds QR 코드 ID 목록
     * @param activeOnly 활성화된 문제만 조회할지 여부
     * @return 매핑 목록
     */
    public List<QrQuizMapping> findMappingsByQrCodeIds(List<String> qrCodeIds, Boolean activeOnly) {
        log.debug("Finding mappings by QR IDs: {}, activeOnly: {}", qrCodeIds, activeOnly);

        if (qrCodeIds == null || qrCodeIds.isEmpty()) {
            throw new IllegalArgumentException("QR ID 목록은 필수입니다.");
        }

        // 빈 문자열이나 null 값 제거
        List<String> validQrCodeIds = qrCodeIds.stream()
                .filter(qrCodeId -> qrCodeId != null && !qrCodeId.trim().isEmpty())
                .distinct() // 중복 제거
                .collect(java.util.stream.Collectors.toList());

        if (validQrCodeIds.isEmpty()) {
            throw new IllegalArgumentException("유효한 QR ID가 없습니다.");
        }

        return qrQuizMappingMapper.selectMappingsByQrCodeIds(validQrCodeIds, activeOnly);
    }

    /**
     * 문제 ID로 매핑 목록 조회
     * @param quizId 문제 ID
     * @return 매핑 목록
     */
    public List<QrQuizMapping> findMappingsByQuizId(Long quizId) {
        log.debug("Finding mappings by Quiz ID: {}", quizId);
        
        if (quizId == null) {
            throw new IllegalArgumentException("문제 ID는 필수입니다.");
        }
        
        return qrQuizMappingMapper.selectMappingsByQuizId(quizId);
    }

    /**
     * 매핑 ID로 단일 매핑 조회
     * @param mappingId 매핑 ID
     * @return 매핑 정보
     */
    public QrQuizMapping findMappingById(Long mappingId) {
        log.debug("Finding mapping by ID: {}", mappingId);
        
        if (mappingId == null) {
            throw new IllegalArgumentException("매핑 ID는 필수입니다.");
        }
        
        QrQuizMapping mapping = qrQuizMappingMapper.selectMappingById(mappingId);
        if (mapping == null) {
            throw new IllegalArgumentException("존재하지 않는 매핑입니다. ID: " + mappingId);
        }
        
        return mapping;
    }

    /**
     * QR-문제 매핑 생성
     * @param mapping 매핑 정보
     * @return 생성된 매핑 정보
     */
    @Transactional
    public QrQuizMapping createMapping(QrQuizMapping mapping) {
        log.debug("Creating QR-Quiz mapping: {}", mapping);

        validateMapping(mapping);

        // 문제 존재 여부 확인
        quizService.findQuizById(mapping.getQuizId());

        // 중복 매핑 체크
        QrQuizMapping existingMapping = qrQuizMappingMapper.selectMappingByQrCodeIdAndQuizId(
                mapping.getQrCodeId(), mapping.getQuizId());
        if (existingMapping != null) {
            throw new IllegalArgumentException("이미 연결된 QR-문제 조합입니다.");
        }

        // 매핑 등록
        int result = qrQuizMappingMapper.insertMapping(mapping);
        if (result != 1) {
            throw new RuntimeException("QR-문제 매핑 등록에 실패했습니다.");
        }

        log.info("QR-Quiz mapping created successfully. ID: {}", mapping.getMappingId());
        return mapping;
    }

    /**
     * QR-문제 매핑 수정
     * @param mapping 매핑 정보
     * @return 수정된 매핑 정보
     */
    @Transactional
    public QrQuizMapping updateMapping(QrQuizMapping mapping) {
        log.debug("Updating QR-Quiz mapping: {}", mapping);

        if (mapping.getMappingId() == null) {
            throw new IllegalArgumentException("매핑 ID는 필수입니다.");
        }

        // 기존 매핑 존재 여부 확인
        findMappingById(mapping.getMappingId());

        // 매핑 수정
        int result = qrQuizMappingMapper.updateMapping(mapping);
        if (result != 1) {
            throw new RuntimeException("QR-문제 매핑 수정에 실패했습니다.");
        }

        log.info("QR-Quiz mapping updated successfully. ID: {}", mapping.getMappingId());
        return mapping;
    }

    /**
     * QR-문제 매핑 삭제 (논리 삭제)
     * @param mappingId 매핑 ID
     * @param deleteId 삭제자 ID
     */
    @Transactional
    public void deleteMapping(Long mappingId, String deleteId) {
        log.debug("Deleting QR-Quiz mapping. ID: {}, deleteId: {}", mappingId, deleteId);

        if (mappingId == null) {
            throw new IllegalArgumentException("매핑 ID는 필수입니다.");
        }

        if (deleteId == null || deleteId.trim().isEmpty()) {
            throw new IllegalArgumentException("삭제자 ID는 필수입니다.");
        }

        // 기존 매핑 존재 여부 확인
        findMappingById(mappingId);

        // 매핑 삭제
        int result = qrQuizMappingMapper.deleteMapping(mappingId, deleteId);
        if (result != 1) {
            throw new RuntimeException("QR-문제 매핑 삭제에 실패했습니다.");
        }

        log.info("QR-Quiz mapping deleted successfully. ID: {}", mappingId);
    }

    /**
     * QR ID와 문제 ID로 특정 매핑 삭제 (논리 삭제)
     * @param qrCodeId QR 코드 ID
     * @param quizId 문제 ID
     * @param deleteId 삭제자 ID
     * @return 삭제된 매핑 정보
     */
    @Transactional
    public QrQuizMapping deleteMappingByQrCodeIdAndQuizId(Long qrCodeId, Long quizId, String deleteId) {
        log.debug("Deleting QR-Quiz mapping by QR ID and Quiz ID. qrCodeId: {}, QuizId: {}, deleteId: {}",
                qrCodeId, quizId, deleteId);

        if (qrCodeId == null || qrCodeId <= 0) {
            throw new IllegalArgumentException("QR ID는 필수입니다.");
        }

        if (quizId == null) {
            throw new IllegalArgumentException("문제 ID는 필수입니다.");
        }

        if (deleteId == null || deleteId.trim().isEmpty()) {
            throw new IllegalArgumentException("삭제자 ID는 필수입니다.");
        }

        // 기존 매핑 존재 여부 확인 및 정보 조회
        QrQuizMapping existingMapping = qrQuizMappingMapper.selectMappingByQrCodeIdAndQuizId(qrCodeId, quizId);
        if (existingMapping == null) {
            throw new IllegalArgumentException("해당 QR 코드와 문제의 연결이 존재하지 않습니다.");
        }

        // 매핑 삭제
        int result = qrQuizMappingMapper.deleteMapping(existingMapping.getMappingId(), deleteId);
        if (result != 1) {
            throw new RuntimeException("QR-문제 매핑 삭제에 실패했습니다.");
        }

        log.info("QR-Quiz mapping deleted successfully. qrCodeId: {}, QuizId: {}, MappingId: {}",
                qrCodeId, quizId, existingMapping.getMappingId());

        return existingMapping;
    }

    /**
     * QR ID로 모든 매핑 삭제 (논리 삭제)
     * @param qrCodeId QR 코드 ID
     * @param deleteId 삭제자 ID
     */
    @Transactional
    public void deleteMappingsByQrCodeId(Long qrCodeId, String deleteId) {
        log.debug("Deleting all mappings by QR ID: {}, deleteId: {}", qrCodeId, deleteId);

        if (qrCodeId == null || qrCodeId <= 0) {
            throw new IllegalArgumentException("QR ID는 필수입니다.");
        }

        if (deleteId == null || deleteId.trim().isEmpty()) {
            throw new IllegalArgumentException("삭제자 ID는 필수입니다.");
        }

        int result = qrQuizMappingMapper.deleteMappingsByQrCodeId(qrCodeId, deleteId);
        log.info("Deleted {} mappings for QR ID: {}", result, qrCodeId);
    }

    /**
     * 문제 ID로 모든 매핑 삭제 (논리 삭제)
     * @param quizId 문제 ID
     * @param deleteId 삭제자 ID
     */
    @Transactional
    public void deleteMappingsByQuizId(Long quizId, String deleteId) {
        log.debug("Deleting all mappings by Quiz ID: {}, deleteId: {}", quizId, deleteId);

        if (quizId == null) {
            throw new IllegalArgumentException("문제 ID는 필수입니다.");
        }

        if (deleteId == null || deleteId.trim().isEmpty()) {
            throw new IllegalArgumentException("삭제자 ID는 필수입니다.");
        }

        int result = qrQuizMappingMapper.deleteMappingsByQuizId(quizId, deleteId);
        log.info("Deleted {} mappings for Quiz ID: {}", result, quizId);
    }

    /**
     * 매핑 정보 유효성 검증
     * @param mapping 매핑 정보
     */
    private void validateMapping(QrQuizMapping mapping) {
        if (mapping == null) {
            throw new IllegalArgumentException("매핑 정보는 필수입니다.");
        }

        if (mapping.getQrCodeId() == null || mapping.getQrCodeId() <= 0) {
            throw new IllegalArgumentException("QR ID는 필수입니다.");
        }

        if (mapping.getQuizId() == null) {
            throw new IllegalArgumentException("문제 ID는 필수입니다.");
        }

        if (mapping.getCreateId() == null || mapping.getCreateId().trim().isEmpty()) {
            throw new IllegalArgumentException("생성자 ID는 필수입니다.");
        }
    }
}
