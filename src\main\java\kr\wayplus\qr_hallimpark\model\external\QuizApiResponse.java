package kr.wayplus.qr_hallimpark.model.external;

import kr.wayplus.qr_hallimpark.model.Quiz;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * - 문제 정보 (외부 노출용)
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuizApiResponse {

    private Long quizId;
    private String title;
    private String question;
    private String categoryName;
    private String parentCategoryName;
    private String categoryPath;

    /**
     * 문제 유형
     * - MCQ: 객관식
     * - OX: OX 퀴즈
     * - ORDER: 순서 정렬
     * - IMAGE_VOICE: 이미지/음성 인식
     * - PUZZLE_MEMORY: 퍼즐/기억력 게임
     */
    private String quizType;

    /**
     * 문제 상태
     * - ACTIVE: 활성
     * - INACTIVE: 비활성
     */
    private String status;

    /**
     * 난이도 (1~5)
     */
    private Integer difficultyLevel;

    /**
     * Quiz 모델에서 QuizApiResponse로 변환
     * @param quiz Quiz 모델
     * @return QuizApiResponse
     */
    public static QuizApiResponse fromQuiz(Quiz quiz) {
        return QuizApiResponse.builder()
                .quizId(quiz.getQuizId())
                .title(quiz.getTitle())
                .question(quiz.getQuestion())
                .categoryName(quiz.getCategoryName())
                .parentCategoryName(quiz.getParentCategoryName())
                .categoryPath(quiz.getCategoryPath())
                .quizType(quiz.getQuizType())
                .status(quiz.getStatus())
                .difficultyLevel(quiz.getDifficultyLevel())
                .build();
    }
}
