package kr.wayplus.qr_hallimpark.config;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.CacheControl;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.view.json.MappingJackson2JsonView;

import kr.wayplus.qr_hallimpark.config.handler.DataProcessingInterceptor;
import kr.wayplus.qr_hallimpark.config.handler.TokenAuthorizeInterceptor;
import kr.wayplus.qr_hallimpark.config.UserPageCommonInterceptor;

@Configuration
public class ApplicationConfig implements WebMvcConfigurer {

	@Value("${upload.path.file}")
	String externalFileUploadPath = "/test";
	@Value("${upload.path.image}")
	String externalImageUploadPath = "/test";

	private TokenAuthorizeInterceptor tokenAuthorizeInterceptor;
	private UserPageCommonInterceptor userPageCommonInterceptor;

	@Autowired

	public ApplicationConfig(
			TokenAuthorizeInterceptor tokenAuthorizeInterceptor,
			UserPageCommonInterceptor userPageCommonInterceptor) {
		this.tokenAuthorizeInterceptor = tokenAuthorizeInterceptor;
		this.userPageCommonInterceptor = userPageCommonInterceptor;
	}

	@Override
	public void addResourceHandlers(ResourceHandlerRegistry registry){
		//외부 저장소 설정
		registry.addResourceHandler("/upload/file/**" )
				.setCacheControl(CacheControl.maxAge(60, TimeUnit.SECONDS))
				.addResourceLocations("file:///"+ externalFileUploadPath);
		registry.addResourceHandler("/upload/images/**")
				.setCacheControl(CacheControl.maxAge(60, TimeUnit.SECONDS))
				.addResourceLocations("file:///"+externalImageUploadPath);

		//static 리소스 경로 설정
		registry.addResourceHandler("/js/**")
				.addResourceLocations("classpath:/static/js/")
				.setCacheControl(CacheControl.maxAge(365, TimeUnit.DAYS));
		registry.addResourceHandler("/css/**")
				.addResourceLocations("classpath:/static/css/")
				.setCacheControl(CacheControl.maxAge(365, TimeUnit.DAYS));
		registry.addResourceHandler("/font/**")
				.addResourceLocations("classpath:/static/font/")
				.setCacheControl(CacheControl.maxAge(365, TimeUnit.DAYS));
		registry.addResourceHandler("/images/**")
				.addResourceLocations("classpath:/static/images/")
				.setCacheControl(CacheControl.maxAge(365, TimeUnit.DAYS));
		registry.addResourceHandler("/icon/**")
				.addResourceLocations("classpath:/static/icon/")
				.setCacheControl(CacheControl.maxAge(365, TimeUnit.DAYS));
		registry.addResourceHandler("/plugin/**")
				.addResourceLocations("classpath:/static/plugin/")
				.setCacheControl(CacheControl.maxAge(365, TimeUnit.DAYS));
		registry.addResourceHandler("/file/**")
				.addResourceLocations("classpath:/static/file/")
				.setCacheControl(CacheControl.maxAge(365, TimeUnit.DAYS));
		registry.addResourceHandler("/video/**")
				.addResourceLocations("classpath:/static/video/")
				.setCacheControl(CacheControl.maxAge(365, TimeUnit.DAYS));
	}

	@Override
	public void addInterceptors(InterceptorRegistry registry){
		List<String> excludeUrl = new ArrayList<>();
		excludeUrl.add("/plugin/**");
		excludeUrl.add("/images/**");
		excludeUrl.add("/icon/**");
		excludeUrl.add("/css/**");
		excludeUrl.add("/font/**");
		excludeUrl.add("/js/**");
		excludeUrl.add("/file/**");
		excludeUrl.add("/video/**");
		excludeUrl.add("/upload/**");

		registry.addInterceptor(tokenAuthorizeInterceptor)
				.addPathPatterns("/**")
				.excludePathPatterns(excludeUrl);

		registry.addInterceptor(userPageCommonInterceptor)
				.excludePathPatterns(excludeUrl)
				.addPathPatterns("/**");

	}

	@Bean
	public ConfigurableServletWebServerFactory webServerFactory() {
		AtomicReference<TomcatServletWebServerFactory> factory = new AtomicReference<>(new TomcatServletWebServerFactory());
		factory.get().addConnectorCustomizers(connector -> connector.setProperty("relaxedQueryChars", "|{}"));
		return factory.get();
	}

	@Bean
	public MappingJackson2JsonView jsonView(){
		MappingJackson2JsonView mappingJackson2JsonView = new MappingJackson2JsonView();
		mappingJackson2JsonView.setContentType("application/json;charset=utf-8");
		mappingJackson2JsonView.setPrettyPrint(true);
		return mappingJackson2JsonView;
	}

}
