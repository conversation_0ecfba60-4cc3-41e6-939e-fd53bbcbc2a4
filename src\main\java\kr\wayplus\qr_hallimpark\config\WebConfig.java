package kr.wayplus.qr_hallimpark.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC 설정
 * - CORS 설정 추가
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // 외부 프론트엔드용 공개 API CORS 설정
        registry.addMapping("/api/public/**")
                .allowedOrigins("http://192.168.0.227:9998")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);

        // 문제 목록 API CORS 설정
        registry.addMapping("/api/quizzes/**")
                .allowedOrigins("http://192.168.0.227:9998")
                .allowedMethods("GET", "POST", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);

        // 서버 간 통신용 API CORS 설정 (더 제한적)
        registry.addMapping("/api/qr-quiz/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "DELETE", "OPTIONS")
                .allowedHeaders("Content-Type", "X-API-Key", "Authorization")
                .allowCredentials(false)
                .maxAge(3600);
    }
}
