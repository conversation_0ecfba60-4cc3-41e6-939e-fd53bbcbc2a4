package kr.wayplus.qr_hallimpark.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import kr.wayplus.qr_hallimpark.model.external.QrQuizMapping;

import java.util.List;

/**
 * QR-문제 매핑 매퍼
 * - qr_quiz_mapping 테이블 CRUD 처리
 * - QR 코드와 문제 간의 연결 정보 관리
 */
@Mapper
@Repository
public interface QrQuizMappingMapper {
    
    /**
     * QR ID로 매핑 목록 조회
     * @param qrCodeId QR 코드 ID
     * @return 매핑 목록
     */
    List<QrQuizMapping> selectMappingsByQrCodeId(Long qrCodeId);

    /**
     * 여러 QR ID로 매핑 목록 조회
     * @param qrCodeIds QR 코드 ID 목록
     * @param activeOnly 활성화된 문제만 조회할지 여부
     * @return 매핑 목록
     */
    List<QrQuizMapping> selectMappingsByQrCodeIds(@Param("qrCodeIds") List<String> qrCodeIds, @Param("activeOnly") Boolean activeOnly);
    
    /**
     * 문제 ID로 매핑 목록 조회
     * @param quizId 문제 ID
     * @return 매핑 목록
     */
    List<QrQuizMapping> selectMappingsByQuizId(Long quizId);
    
    /**
     * 매핑 ID로 단일 매핑 조회
     * @param mappingId 매핑 ID
     * @return 매핑 정보
     */
    QrQuizMapping selectMappingById(Long mappingId);
    
    /**
     * QR ID와 문제 ID로 매핑 조회
     * @param qrCodeId QR 코드 ID
     * @param quizId 문제 ID
     * @return 매핑 정보
     */
    QrQuizMapping selectMappingByQrCodeIdAndQuizId(@Param("qrCodeId") Long qrCodeId, @Param("quizId") Long quizId);
    
    /**
     * 매핑 등록
     * @param mapping 매핑 정보
     * @return 등록된 행 수
     */
    int insertMapping(QrQuizMapping mapping);
    
    /**
     * 매핑 수정
     * @param mapping 매핑 정보
     * @return 수정된 행 수
     */
    int updateMapping(QrQuizMapping mapping);
    
    /**
     * 매핑 삭제 (논리 삭제)
     * @param mappingId 매핑 ID
     * @param deleteId 삭제자 ID
     * @return 삭제된 행 수
     */
    int deleteMapping(@Param("mappingId") Long mappingId, @Param("deleteId") String deleteId);
    
    /**
     * QR ID로 모든 매핑 삭제 (논리 삭제)
     * @param qrCodeId QR 코드 ID
     * @param deleteId 삭제자 ID
     * @return 삭제된 행 수
     */
    int deleteMappingsByQrCodeId(@Param("qrCodeId") Long qrCodeId, @Param("deleteId") String deleteId);
    
    /**
     * 문제 ID로 모든 매핑 삭제 (논리 삭제)
     * @param quizId 문제 ID
     * @param deleteId 삭제자 ID
     * @return 삭제된 행 수
     */
    int deleteMappingsByQuizId(@Param("quizId") Long quizId, @Param("deleteId") String deleteId);
}
