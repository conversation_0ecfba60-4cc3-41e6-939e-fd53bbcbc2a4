package kr.wayplus.qr_hallimpark.model.external;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * QR-문제 매핑 모델
 * - qr_quiz_mapping 테이블에 대응
 * - QR 코드와 문제 간의 연결 정보 관리
 * - 모든 관련 하위 모델들을 내부 클래스로 포함
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QrQuizMapping {

    private Long mappingId;
    private Long qrCodeId;
    private List<String> qrCodeIds;
    private Long quizId;
    private Integer displayOrder;
    private String createId;
    private LocalDateTime createDate;
    private String lastUpdateId;
    private LocalDateTime lastUpdateDate;
    private String deleteYn;
    private String deleteId;
    private String sortBy;
    private LocalDateTime deleteDate;
    
    // ========== 조인 필드 (조회 시 사용) ==========
    private String quizTitle;
    private String quizType;
    private String quizStatus;
    private String categoryName;
    private String question;
    private Integer difficultyLevel;
    private String correctAnswer;
    private String imageUrl;
    private String hint;

    /**
     * @param createId 생성자 ID
     * @return QrQuizMapping
     */
    public QrQuizMapping toQrQuizMapping() {
        return QrQuizMapping.builder()
                .qrCodeId(this.qrCodeId)
                .quizId(this.quizId)
                .displayOrder(this.displayOrder)
                .createId(this.createId)
                .build();
    }

    /**
     * 여러 QR 코드의 연결된 문제 조회 요청 모델
     * - 외부에서 여러 QR 코드 ID를 JSON으로 전송할 때 사용
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QrQuizInfoRequest {

        private List<String> qrCodeIds;
        private String sortBy;
        @Builder.Default
        private Boolean activeOnly = true;
    }

    /**
     * 여러 QR 코드의 연결된 문제 조회 응답 모델
     * - QR 코드별로 연결된 문제 목록을 그룹화하여 반환
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QrQuizInfoResponse {
        private Long qrCodeId;
        private List<QuizInfo> quizzes;
        private Integer quizCount;
    }

    /**
     * QR 코드별 문제 정보
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QuizInfo {

        private Long mappingId;
        private Long quizId;
        private Long qrCodeId;
        private String quizTitle;
        private String question;
        private String quizType;
        private String quizStatus;
        private String categoryName;
        private Integer difficultyLevel;
        private Integer displayOrder;
        private String correctAnswer;
        private String imageUrl;
        private String hint;

        /**
         * QrQuizMapping에서 QuizInfo로 변환
         * @param mapping QrQuizMapping 객체
         * @return QuizInfo 객체
         */
        public static QuizInfo fromQrQuizMapping(QrQuizMapping mapping) {
            return QuizInfo.builder()
                    .mappingId(mapping.getMappingId())
                    .quizId(mapping.getQuizId())
                    .qrCodeId(mapping.getQrCodeId())
                    .quizTitle(mapping.getQuizTitle())
                    .question(mapping.getQuestion())
                    .quizType(mapping.getQuizType())
                    .quizStatus(mapping.getQuizStatus())
                    .categoryName(mapping.getCategoryName())
                    .difficultyLevel(mapping.getDifficultyLevel())
                    .displayOrder(mapping.getDisplayOrder())
                    .correctAnswer(mapping.getCorrectAnswer())
                    .imageUrl(mapping.getImageUrl())
                    .hint(mapping.getHint())
                    .build();
        }
    }

    /**
     * QR-문제 연결 생성 응답 모델
     * - API 서버로 반환하는 연결 생성 결과 데이터
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QrQuizMappingResponse {

        private Long mappingId;
        private Long qrCodeId;
        private Long quizId;
        private String quizTitle;
        private Integer displayOrder;

        /**
         * QrQuizMapping에서 QrQuizMappingResponse로 변환
         * @param mapping QrQuizMapping
         * @return QrQuizMappingResponse
         */
        public static QrQuizMappingResponse fromQrQuizMapping(QrQuizMapping mapping) {
            return QrQuizMappingResponse.builder()
                    .mappingId(mapping.getMappingId())
                    .qrCodeId(mapping.getQrCodeId())
                    .quizId(mapping.getQuizId())
                    .quizTitle(mapping.getQuizTitle())
                    .displayOrder(mapping.getDisplayOrder())
                    .build();
        }
    }

    /**
     * QR-문제 연결 해제 요청 모델
     * - API 서버에서 전송하는 연결 해제 요청 데이터
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QrQuizUnMappingRequest {

        private Long qrCodeId;
        private Long quizId;
        private Long mappingId;
        private String createId;
        private String deleteReason;
        public boolean isValid() {
            // mappingId가 있거나, qrCodeId와 quizId가 모두 있어야 함
            return (mappingId != null) ||
                   (qrCodeId != null && qrCodeId > 0 && quizId != null);
        }
    }

    /**
     * QR-문제 연결 해제 응답 모델
     * - API 서버로 반환하는 연결 해제 결과 데이터
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QrQuizUnMappingResponse {
        private Long mappingId;
        private Long qrCodeId;
        private Long quizId;
        private String quizTitle;
        private LocalDateTime deleteDate;
        private String deleteId;
        private String deleteReason;

        /**
         * QrQuizMapping에서 QrQuizUnMappingResponse로 변환
         * @param mapping 삭제 전 QrQuizMapping 정보
         * @param deleteReason 삭제 사유
         * @return QrQuizUnMappingResponse
         */
        public static QrQuizUnMappingResponse fromQrQuizMapping(QrQuizMapping mapping, String deleteReason) {
            return QrQuizUnMappingResponse.builder()
                    .mappingId(mapping.getMappingId())
                    .qrCodeId(mapping.getQrCodeId())
                    .quizId(mapping.getQuizId())
                    .quizTitle(mapping.getQuizTitle())
                    .deleteDate(LocalDateTime.now())
                    .deleteId(mapping.getCreateId())
                    .deleteReason(deleteReason)
                    .build();
        }
    }
}
